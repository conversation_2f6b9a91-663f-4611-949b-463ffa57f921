package mesfavoris.internal.settings.placeholders;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.ui.ColoredListCellRenderer;
import com.intellij.ui.DoubleClickListener;
import com.intellij.ui.SimpleTextAttributes;
import com.intellij.ui.ToolbarDecorator;
import com.intellij.ui.components.JBList;
import com.intellij.util.ui.JBUI;
import mesfavoris.internal.placeholders.PathPlaceholderResolver;
import mesfavoris.model.Bookmark;
import mesfavoris.model.BookmarkFolder;
import mesfavoris.model.BookmarkId;
import mesfavoris.model.BookmarksTree;
import mesfavoris.path.PathBookmarkProperties;
import mesfavoris.placeholders.IPathPlaceholderResolver;
import mesfavoris.placeholders.PathPlaceholder;
import mesfavoris.service.BookmarksService;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Panel with list to configure placeholders with statistics (for tool window)
 */
public class PlaceholdersListPanel extends JPanel {
    private DefaultListModel<PathPlaceholder> listModel;
    private JBList<PathPlaceholder> list;
    private JPanel mainPanel;
    private PathPlaceholderStats stats;
    private final Project contextProject; // Project for statistics context

    // Bookmark management components
    private JPanel bookmarkManagementPanel;
    private DefaultListModel<Bookmark> collapsableBookmarksModel;
    private DefaultListModel<Bookmark> collapsedBookmarksModel;
    private JBList<Bookmark> collapsableBookmarksList;
    private JBList<Bookmark> collapsedBookmarksList;
    private IPathPlaceholderResolver pathPlaceholderResolver;

    public PlaceholdersListPanel() {
        this(null);
    }

    public PlaceholdersListPanel(@Nullable Project project) {
        super(new BorderLayout());
        this.contextProject = project;
        initComponents(project);
        layoutComponents();
    }

    private void initComponents(@Nullable Project project) {
        // Initialize stats with BookmarksService and file path property
        List<String> pathPropertyNames = Arrays.asList(PathBookmarkProperties.PROP_FILE_PATH);

        if (project != null && !project.isDisposed()) {
            try {
                BookmarksService bookmarksService = project.getService(BookmarksService.class);
                if (bookmarksService != null) {
                    stats = new PathPlaceholderStats(bookmarksService::getBookmarksTree, pathPropertyNames);
                } else {
                    stats = createFallbackStats(pathPropertyNames);
                }
            } catch (Exception e) {
                stats = createFallbackStats(pathPropertyNames);
            }
        } else {
            stats = createFallbackStats(pathPropertyNames);
        }

        // Initialize path placeholder resolver
        PathPlaceholdersStore placeholdersStore = PathPlaceholdersStore.getInstance();
        pathPlaceholderResolver = new PathPlaceholderResolver(placeholdersStore);

        listModel = new DefaultListModel<>();
        list = new JBList<>(listModel);
        list.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        // Custom cell renderer to show placeholder name, usage count, and path
        list.setCellRenderer(new PlaceholderListCellRenderer());

        // Add selection listener to update bookmark lists
        list.addListSelectionListener(new ListSelectionListener() {
            @Override
            public void valueChanged(ListSelectionEvent e) {
                if (!e.getValueIsAdjusting()) {
                    updateBookmarkLists();
                }
            }
        });

        // Add double-click listener to edit placeholders
        new DoubleClickListener() {
            @Override
            protected boolean onDoubleClick(@NotNull MouseEvent event) {
                int selectedIndex = list.getSelectedIndex();
                if (selectedIndex >= 0) {
                    editPlaceholder(selectedIndex);
                    return true;
                }
                return false;
            }
        }.installOn(list);

        // Initialize bookmark management components
        initBookmarkManagementComponents();
    }

    private void layoutComponents() {
        ToolbarDecorator decorator = ToolbarDecorator.createDecorator(list)
            .setAddAction(button -> addPlaceholder())
            .setEditAction(button -> {
                int selectedIndex = list.getSelectedIndex();
                if (selectedIndex >= 0) {
                    editPlaceholder(selectedIndex);
                }
            })
            .setRemoveAction(button -> {
                int selectedIndex = list.getSelectedIndex();
                if (selectedIndex >= 0) {
                    removePlaceholder(selectedIndex);
                }
            })
            .setEditActionUpdater(e -> list.getSelectedIndex() >= 0)
            .setRemoveActionUpdater(e -> list.getSelectedIndex() >= 0);

        mainPanel = decorator.createPanel();

        // Create main content panel with placeholders at top and bookmark management at bottom
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.add(mainPanel, BorderLayout.CENTER);

        // Set preferred height for bookmark management panel and ensure it resizes properly
        bookmarkManagementPanel.setPreferredSize(JBUI.size(-1, 200)); // -1 means use parent width
        contentPanel.add(bookmarkManagementPanel, BorderLayout.SOUTH);

        add(contentPanel, BorderLayout.CENTER);

        // Add explanatory label
        String helpText = "Placeholders allow you to define shortcuts for frequently used paths. " +
            "For example, a 'HOME' placeholder pointing to '/home/<USER>' will allow you to use '${HOME}/documents' " +
            "in bookmarks.";

        if (contextProject != null) {
            helpText += " Usage statistics are shown for the current project.";
        }

        JLabel helpLabel = new JLabel("<html><body style='width: 400px'>" + helpText + "</body></html>");
        helpLabel.setBorder(JBUI.Borders.emptyBottom(10));
        add(helpLabel, BorderLayout.NORTH);
    }

    public void setPlaceholders(List<PathPlaceholder> placeholders) {
        listModel.clear();
        for (PathPlaceholder placeholder : placeholders) {
            listModel.addElement(placeholder);
        }
    }

    public List<PathPlaceholder> getPlaceholders() {
        List<PathPlaceholder> result = new ArrayList<>();
        for (int i = 0; i < listModel.getSize(); i++) {
            result.add(listModel.getElementAt(i));
        }
        return result;
    }

    private void addPlaceholder() {
        Project project = getAvailableProject();
        List<PathPlaceholder> existingPlaceholders = getPlaceholders();
        PlaceholderEditDialog dialog = new PlaceholderEditDialog(project, existingPlaceholders);

        if (dialog.showAndGet()) {
            PathPlaceholder newPlaceholder = dialog.getPlaceholder();
            if (newPlaceholder != null) {
                listModel.addElement(newPlaceholder);
                int newIndex = listModel.getSize() - 1;
                list.setSelectedIndex(newIndex);
            }
        }
    }

    private void editPlaceholder(int selectedIndex) {
        PathPlaceholder currentPlaceholder = listModel.getElementAt(selectedIndex);
        if (currentPlaceholder != null) {
            Project project = getAvailableProject();
            List<PathPlaceholder> existingPlaceholders = getPlaceholders();
            PlaceholderEditDialog dialog = new PlaceholderEditDialog(project, currentPlaceholder, existingPlaceholders);

            if (dialog.showAndGet()) {
                PathPlaceholder updatedPlaceholder = dialog.getPlaceholder();
                if (updatedPlaceholder != null) {
                    listModel.setElementAt(updatedPlaceholder, selectedIndex);
                }
            }
        }
    }

    @Nullable
    private Project getAvailableProject() {
        // Try to get the first open project
        Project[] openProjects = ProjectManager.getInstance().getOpenProjects();
        if (openProjects.length > 0) {
            return openProjects[0];
        }
        return null;
    }

    private void removePlaceholder(int selectedIndex) {
        listModel.removeElementAt(selectedIndex);
    }

    private PathPlaceholderStats createFallbackStats(List<String> pathPropertyNames) {
        // Create stats with empty bookmarks tree for fallback
        BookmarkFolder rootFolder = new BookmarkFolder(new BookmarkId("root"), "Root");
        BookmarksTree emptyTree = new BookmarksTree(rootFolder);
        return new PathPlaceholderStats(() -> emptyTree, pathPropertyNames);
    }

    private void initBookmarkManagementComponents() {
        bookmarkManagementPanel = new JPanel(new BorderLayout());
        bookmarkManagementPanel.setBorder(JBUI.Borders.emptyTop(10));

        // Initialize list models
        collapsableBookmarksModel = new DefaultListModel<>();
        collapsedBookmarksModel = new DefaultListModel<>();

        // Initialize lists
        collapsableBookmarksList = new JBList<>(collapsableBookmarksModel);
        collapsedBookmarksList = new JBList<>(collapsedBookmarksModel);

        // Set cell renderers for bookmark lists
        collapsableBookmarksList.setCellRenderer(new BookmarkListCellRenderer());
        collapsedBookmarksList.setCellRenderer(new BookmarkListCellRenderer());

        // Create the bookmark management UI
        createBookmarkManagementUI();
    }

    private void createBookmarkManagementUI() {
        // Use GridBagLayout for better control over resizing
        bookmarkManagementPanel.setLayout(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();

        // Left panel - Bookmarks with collapsable paths
        JPanel leftPanel = new JPanel(new BorderLayout());
        leftPanel.add(new JLabel("Bookmarks with collapsable paths"), BorderLayout.NORTH);
        JScrollPane leftScrollPane = new JScrollPane(collapsableBookmarksList);
        leftScrollPane.setMinimumSize(JBUI.size(200, 150));
        leftPanel.add(leftScrollPane, BorderLayout.CENTER);

        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.weightx = 0.45; // 45% of horizontal space
        gbc.weighty = 1.0;  // Full vertical space
        gbc.fill = GridBagConstraints.BOTH;
        gbc.insets = JBUI.insets(5);
        bookmarkManagementPanel.add(leftPanel, gbc);

        // Center panel - Transfer buttons
        JPanel buttonPanel = new JPanel(new GridBagLayout());
        GridBagConstraints buttonGbc = new GridBagConstraints();
        buttonGbc.insets = JBUI.insets(5);
        buttonGbc.gridx = 0;
        buttonGbc.gridy = 0;
        buttonGbc.fill = GridBagConstraints.HORIZONTAL;

        JButton addButton = new JButton("Add ▶");
        addButton.setEnabled(false); // Disabled for now
        buttonPanel.add(addButton, buttonGbc);

        buttonGbc.gridy++;
        JButton addAllButton = new JButton("Add All ▶▶");
        addAllButton.setEnabled(false); // Disabled for now
        buttonPanel.add(addAllButton, buttonGbc);

        buttonGbc.gridy++;
        JButton removeButton = new JButton("◀ Remove");
        removeButton.setEnabled(false); // Disabled for now
        buttonPanel.add(removeButton, buttonGbc);

        buttonGbc.gridy++;
        JButton removeAllButton = new JButton("◀◀ Remove All");
        removeAllButton.setEnabled(false); // Disabled for now
        buttonPanel.add(removeAllButton, buttonGbc);

        gbc.gridx = 1;
        gbc.weightx = 0.1; // 10% of horizontal space for buttons
        gbc.fill = GridBagConstraints.VERTICAL;
        bookmarkManagementPanel.add(buttonPanel, gbc);

        // Right panel - Bookmarks with collapsed paths
        JPanel rightPanel = new JPanel(new BorderLayout());
        rightPanel.add(new JLabel("Bookmarks with collapsed paths"), BorderLayout.NORTH);
        JScrollPane rightScrollPane = new JScrollPane(collapsedBookmarksList);
        rightScrollPane.setMinimumSize(JBUI.size(200, 150));
        rightPanel.add(rightScrollPane, BorderLayout.CENTER);

        gbc.gridx = 2;
        gbc.weightx = 0.45; // 45% of horizontal space
        gbc.fill = GridBagConstraints.BOTH;
        bookmarkManagementPanel.add(rightPanel, gbc);

        // Initially hidden until a placeholder is selected
        bookmarkManagementPanel.setVisible(false);
    }

    private void updateBookmarkLists() {
        PathPlaceholder selectedPlaceholder = list.getSelectedValue();
        if (selectedPlaceholder == null) {
            bookmarkManagementPanel.setVisible(false);
            return;
        }

        bookmarkManagementPanel.setVisible(true);

        // Clear existing lists
        collapsableBookmarksModel.clear();
        collapsedBookmarksModel.clear();

        // Get bookmarks from the current project
        if (contextProject != null && !contextProject.isDisposed()) {
            try {
                BookmarksService bookmarksService = contextProject.getService(BookmarksService.class);
                if (bookmarksService != null) {
                    BookmarksTree bookmarksTree = bookmarksService.getBookmarksTree();
                    populateBookmarkLists(bookmarksTree, selectedPlaceholder);
                }
            } catch (Exception e) {
                // Handle gracefully if service is not available
            }
        }

        // Refresh the UI
        revalidate();
        repaint();
    }

    private void populateBookmarkLists(BookmarksTree bookmarksTree, PathPlaceholder selectedPlaceholder) {
        for (Bookmark bookmark : bookmarksTree) {
            if (bookmark instanceof BookmarkFolder) {
                continue; // Skip folders
            }

            String filePath = bookmark.getPropertyValue(PathBookmarkProperties.PROP_FILE_PATH);
            if (filePath == null || filePath.trim().isEmpty()) {
                continue; // Skip bookmarks without file paths
            }

            // Check if bookmark already uses the selected placeholder
            String placeholderName = PathPlaceholderResolver.getPlaceholderName(filePath);
            if (selectedPlaceholder.getName().equals(placeholderName)) {
                collapsedBookmarksModel.addElement(bookmark);
            } else {
                // Check if the bookmark's path can be collapsed with the selected placeholder
                try {
                    Path expandedPath = pathPlaceholderResolver.expand(filePath);
                    if (expandedPath != null && expandedPath.startsWith(selectedPlaceholder.getPath())) {
                        collapsableBookmarksModel.addElement(bookmark);
                    }
                } catch (Exception e) {
                    // Skip bookmarks with invalid paths
                }
            }
        }
    }

    /**
     * Custom cell renderer for placeholder list items
     */
    private class PlaceholderListCellRenderer extends ColoredListCellRenderer<PathPlaceholder> {
        @Override
        protected void customizeCellRenderer(@NotNull JList<? extends PathPlaceholder> list, PathPlaceholder placeholder,
                                           int index, boolean selected, boolean hasFocus) {
            if (placeholder != null) {
                // Placeholder name
                append(placeholder.getName(), SimpleTextAttributes.REGULAR_BOLD_ATTRIBUTES);

                // Show usage statistics only when we have a project context
                if (contextProject != null) {
                    int usageCount = stats.getUsageCount(placeholder.getName());

                    // Usage count in different color
                    if (usageCount > 0) {
                        append(" (" + usageCount + " matches)", SimpleTextAttributes.GRAYED_ATTRIBUTES);
                    } else {
                        append(" (unused)", SimpleTextAttributes.GRAYED_ATTRIBUTES);
                    }
                }

                // Path
                append(" - " + placeholder.getPath().toString(), SimpleTextAttributes.REGULAR_ATTRIBUTES);
            }
        }
    }

    /**
     * Custom cell renderer for bookmark list items
     */
    private class BookmarkListCellRenderer extends ColoredListCellRenderer<Bookmark> {
        @Override
        protected void customizeCellRenderer(@NotNull JList<? extends Bookmark> list, Bookmark bookmark,
                                           int index, boolean selected, boolean hasFocus) {
            if (bookmark != null) {
                // Bookmark name
                String name = bookmark.getPropertyValue(Bookmark.PROPERTY_NAME);
                if (name != null && !name.trim().isEmpty()) {
                    append(name, SimpleTextAttributes.REGULAR_BOLD_ATTRIBUTES);
                } else {
                    append("Unnamed Bookmark", SimpleTextAttributes.GRAYED_ATTRIBUTES);
                }

                // File path
                String filePath = bookmark.getPropertyValue(PathBookmarkProperties.PROP_FILE_PATH);
                if (filePath != null) {
                    append(" - " + filePath, SimpleTextAttributes.REGULAR_ATTRIBUTES);
                }
            }
        }
    }

}
