<idea-plugin>
    <extensions defaultExtensionNs="com.intellij">
        <toolWindow id="mesfavoris" secondary="true" icon="AllIcons.General.Modified" anchor="right"
                    factoryClass="mesfavoris.internal.toolwindow.MesFavorisToolWindowFactory"/>
        <postStartupActivity implementation="mesfavoris.internal.markers.BookmarksHighlighters$BookmarksHighlightersStartupActivity"/>
        <notificationGroup
                id="com.cchabanois.mesfavoris.errors"
                displayType="STICKY_BALLOON"
                isLogByDefault="true"
        />
        <notificationGroup
                id="com.cchabanois.mesfavoris.info"
                displayType="BALLOON"
                isLogByDefault="false"
        />
        <projectService serviceImplementation="mesfavoris.internal.toolwindow.BookmarksTreeComponentStateService"/>
        <applicationService serviceImplementation="mesfavoris.internal.settings.placeholders.PathPlaceholdersStore"/>
        <applicationConfigurable parentId="tools" instance="mesfavoris.internal.settings.MesFavorisConfigurable"
                                 id="mesfavoris.settings" displayName="Mes Favoris"/>
        <applicationConfigurable parentId="mesfavoris.settings" instance="mesfavoris.internal.settings.placeholders.PlaceholdersConfigurable"
                                 id="mesfavoris.settings.placeholders" displayName="Placeholders"/>
    </extensions>
</idea-plugin>